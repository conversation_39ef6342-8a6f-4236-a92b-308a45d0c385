# logictrue-ui-word

## 项目简介
这是一个基于Vue.js的Word文档处理前端应用，提供表格编辑和Word文档导出功能。支持复杂的表格结构，包括单元格合并、数学公式和**嵌入表格**等高级功能。

## 🚀 新功能：嵌入表格导出

### 主要特性
- ✅ **多层嵌套支持**：支持最多3层表格嵌套
- ✅ **智能导出优化**：自动检测嵌套表格并选择合适的导出模式
- ✅ **完善的错误处理**：数据结构验证和友好的错误提示
- ✅ **性能优化**：大数据量的处理性能优化
- ✅ **用户友好界面**：直观的导出配置面板和进度反馈

### 快速体验
```javascript
// 创建包含嵌套表格的数据
const nestedTableData = {
  title: "部门信息表",
  cellRows: [
    [
      { content: "研发部" },
      {
        content: "项目详情",
        nestedTable: {
          enabled: true,
          config: {
            cellRows: [
              [
                { content: "项目A", width: 150 },
                { content: "进行中", width: 100 }
              ],
              [
                { content: "项目B", width: 150 },
                { content: "已完成", width: 100 }
              ]
            ]
          }
        }
      }
    ]
  ]
}

// 导出到Word
import { exportTableToWord } from '@/api/word/export'
const response = await exportTableToWord(nestedTableData)
```

## 技术栈
- Vue 2.x
- Element UI
- Vite
- pnpm
- Apache POI (后端)

## 主要功能

### 基础功能
- 📊 表格数据编辑
- 🔗 单元格合并
- 🧮 数学公式支持
- 📄 Word文档导出

### 高级功能
- 🏗️ **嵌入表格**：在单元格中嵌入子表格
- 🎨 自定义样式和格式
- 📋 批量数据处理
- 🔍 导出预览功能
- ⚡ 性能优化和错误处理

## 开发环境搭建

### 环境要求
- Node.js 16+
- pnpm 7+
- JDK 8+ (后端)

### 前端安装
```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 后端配置
```bash
# 后端路径
cd /home/<USER>/nl-mes/word-nl/logictrue-word

# 启动后端服务
mvn spring-boot:run
```

## 项目结构
```
前端 (logictrue-ui-word/):
├── src/
│   ├── components/
│   │   ├── JsonTableContainer.vue  # 主表格组件（支持嵌套表格）
│   │   └── CellEditor.vue          # 单元格编辑器
│   ├── api/word/
│   │   └── export.js               # Word导出接口（增强版）
│   └── views/
├── docs/                           # 文档目录
│   ├── 嵌入表格导出功能说明.md
│   ├── 嵌入表格导出功能测试用例.md
│   └── 嵌入表格测试数据.js
└── README.md

后端 (logictrue-word/):
├── src/main/java/com/logictrue/word/
│   ├── controller/
│   │   └── WordExportController.java    # 导出控制器（支持复杂表格）
│   ├── service/
│   │   └── WordExportService.java       # 导出服务（嵌套表格处理）
│   └── dto/
│       └── JsonTableExportRequest.java  # 请求DTO（嵌套表格数据结构）
```

## 使用说明

### 基本表格导出
```vue
<template>
  <JsonTableContainer
    :initial-data="tableData"
    @data-change="handleDataChange"
  />
</template>

<script>
import JsonTableContainer from '@/components/JsonTableContainer.vue'

export default {
  components: { JsonTableContainer },
  data() {
    return {
      tableData: {
        headers: [['部门', '人员', '项目']],
        cellRows: [
          [
            { content: '研发部' },
            { content: '20人' },
            { content: '5个项目' }
          ]
        ]
      }
    }
  }
}
</script>
```

### 嵌套表格导出
```vue
<template>
  <div>
    <!-- 表格组件 -->
    <JsonTableContainer
      ref="tableContainer"
      :initial-data="nestedTableData"
    />
    
    <!-- 导出按钮 -->
    <el-button @click="exportWithNestedTables" type="primary">
      导出嵌套表格
    </el-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      nestedTableData: {
        title: "组织架构表",
        headers: [['部门', '详细信息']],
        cellRows: [
          [
            { content: '技术部' },
            {
              content: '团队组成',
              nestedTable: {
                enabled: true,
                config: {
                  cellRows: [
                    [
                      { content: '前端组' },
                      { content: '5人' }
                    ],
                    [
                      { content: '后端组' },
                      { content: '8人' }
                    ]
                  ]
                }
              }
            }
          ]
        ]
      }
    }
  },
  methods: {
    async exportWithNestedTables() {
      try {
        // 获取表格数据
        const exportData = this.$refs.tableContainer.getDataAsJSON({
          includeNestedTables: true,
          includeEmptyRows: false,
          includeMergeInfo: true
        })

        // 导出到Word
        const { exportTableToWord } = await import('@/api/word/export')
        const response = await exportTableToWord(exportData)
        
        // 处理文件下载
        this.downloadFile(response, '嵌套表格导出.docx')
        
        this.$message.success('导出成功！')
      } catch (error) {
        this.$message.error('导出失败：' + error.message)
      }
    },
    
    downloadFile(response, filename) {
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.click()
      window.URL.revokeObjectURL(url)
    }
  }
}
</script>
```

## API文档

### 前端API

#### 基础导出接口
```javascript
import { exportTableToWord } from '@/api/word/export'

// 自动检测并选择合适的导出模式
const response = await exportTableToWord(data)
```

#### 复杂表格导出接口
```javascript
import { exportComplexTableToWord } from '@/api/word/export'

// 专门用于复杂表格（包含嵌套表格）
const response = await exportComplexTableToWord(data)
```

#### 数据验证接口
```javascript
import { validateNestedTableData, analyzeNestedTables } from '@/api/word/export'

// 验证数据结构
const validation = validateNestedTableData(data)
if (!validation.isValid) {
  console.error('验证失败:', validation.errors)
}

// 分析嵌套表格统计
const stats = analyzeNestedTables(data)
console.log('统计信息:', stats)
```

### 后端API

#### 复杂表格导出
```http
POST /word/exportComplexTable
Content-Type: application/json

{
  "title": "复杂表格导出",
  "cellRows": [
    [
      {
        "content": "主内容",
        "nestedTable": {
          "enabled": true,
          "config": {
            "cellRows": [
              [
                {"content": "嵌套内容1"},
                {"content": "嵌套内容2"}
              ]
            ]
          }
        }
      }
    ]
  ]
}
```

## 配置选项

### 导出配置
| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| includeNestedTables | Boolean | true | 是否包含嵌套表格 |
| includeEmptyRows | Boolean | false | 是否包含空行 |
| includeMergeInfo | Boolean | true | 是否包含合并信息 |
| pageOrientation | String | 'LANDSCAPE' | 页面方向 |
| title | String | '表格导出' | 文档标题 |

### 系统限制
| 限制项 | 最大值 | 说明 |
|--------|--------|------|
| 嵌套层级 | 3 | 最大支持3层嵌套 |
| 嵌套表格数量 | 50 | 单个文档最多50个嵌套表格 |
| 嵌套单元格总数 | 1000 | 所有嵌套表格的单元格总数 |

## 文档

- 📖 [功能说明文档](docs/嵌入表格导出功能说明.md)
- 🧪 [测试用例文档](docs/嵌入表格导出功能测试用例.md)
- 📊 [测试数据文件](docs/嵌入表格测试数据.js)

## 更新日志

### v1.1.0 (2024-08-25) - 嵌入表格功能
- ✨ 新增嵌入表格导出功能
- ✨ 支持多层嵌套表格（最多3层）
- ✨ 智能导出模式选择
- ✨ 完善的数据验证和错误处理
- ✨ 性能优化和用户体验改进
- 📚 完整的文档和测试用例

## 许可证
本项目采用 MIT 许可证
