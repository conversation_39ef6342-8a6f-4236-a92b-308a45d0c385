00:10:26.276 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:10:26.289 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:10:40.844 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 907852 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:10:40.850 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:10:42.360 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:10:42.361 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:10:42.361 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:10:42.498 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:10:43.339 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:10:43.699 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:10:44.287 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:10:44.288 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:10:45.007 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:10:45.052 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:10:45.053 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:10:45.195 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:10:45.201 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:10:45.203 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:10:45.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:10:45.204 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:10:45.205 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:10:45.206 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:10:45.209 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:10:45.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:10:45.286 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 5.085 seconds (JVM running for 5.698)
00:10:51.159 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:14:00.580 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:14:00.584 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:19:29.039 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 919555 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:19:29.041 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:19:29.805 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:19:29.806 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:19:29.806 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:19:29.859 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:19:30.387 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:19:30.652 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:19:31.501 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
00:19:31.502 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
00:19:31.745 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
00:19:31.768 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
00:19:31.769 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
00:19:31.833 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
00:19:31.836 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
00:19:31.839 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
00:19:31.840 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
00:19:31.842 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
00:19:31.880 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:19:31.893 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.69 seconds (JVM running for 3.076)
00:19:34.881 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:20:10.510 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,41] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 1
00:20:10.511 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
00:20:10.511 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,53] - 导出当前页面，车辆ID: 0822, 页面顺序: 1
00:20:10.514 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
00:20:10.514 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
00:20:10.519 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
00:20:10.696 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
00:20:10.696 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 11, 总列数: 8
00:20:10.747 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
00:20:10.803 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.808 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.809 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.811 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:10.812 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 151px (2265twips)
00:20:10.814 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.815 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.816 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.817 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:10.818 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
00:20:10.820 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:20:10.823 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:20:10.824 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:20:10.825 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:20:10.825 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
00:20:10.827 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
00:20:10.828 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:20:10.828 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:20:10.829 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:20:10.899 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3184 bytes
00:20:10.906 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,69] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC1%E9%A1%B5_20250825_002010.docx, 大小: 3184 bytes
00:20:52.705 [http-nio-9550-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0822
00:20:52.706 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
00:20:52.706 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0822
00:20:52.711 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
00:20:52.711 [http-nio-9550-exec-9] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
00:20:52.712 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
00:20:52.714 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
00:20:52.715 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 16, 总列数: 8
00:20:52.718 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
00:20:52.724 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.726 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.727 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.728 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
00:20:52.729 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 151px (2265twips)
00:20:52.730 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.731 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.732 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.733 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.734 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
00:20:52.735 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.736 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.736 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.737 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
00:20:52.738 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
00:20:52.738 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
00:20:52.739 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
00:20:52.740 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
00:20:52.741 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
00:20:52.741 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
00:20:52.751 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3299 bytes
00:20:52.756 [http-nio-9550-exec-9] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250825_002052.docx, 大小: 3299 bytes
00:21:28.454 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:21:28.456 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
08:51:00.465 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 26535 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
08:51:00.468 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
08:51:01.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
08:51:01.390 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
08:51:01.391 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
08:51:01.428 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
08:51:01.997 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
08:51:02.262 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
08:51:02.570 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
08:51:02.571 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
08:51:02.832 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
08:51:02.858 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
08:51:02.858 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
08:51:02.922 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
08:51:02.927 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
08:51:02.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
08:51:02.929 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
08:51:02.930 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
08:51:02.931 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
08:51:02.933 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
08:51:02.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
08:51:02.980 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.916 seconds (JVM running for 3.445)
09:00:52.288 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:17:23.560 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:17:23.562 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:28:48.896 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 73640 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:28:48.899 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:28:49.747 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:28:49.747 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:49.747 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:28:49.782 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:50.271 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:28:50.530 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:28:50.945 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:28:50.946 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:28:51.235 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:28:51.256 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:28:51.257 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:28:51.320 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:28:51.324 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:28:51.326 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:28:51.327 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:28:51.328 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:28:51.329 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:28:51.364 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:28:51.378 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.884 seconds (JVM running for 3.264)
09:29:21.169 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:36:28.125 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:36:28.126 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:41:18.219 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:41:18.220 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:41:20.894 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:41:20.894 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:48:42.347 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 98763 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:48:42.349 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:48:43.127 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:48:43.127 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:48:43.128 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:48:43.159 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:48:43.724 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:48:43.925 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:48:44.284 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
09:48:44.285 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
09:48:44.590 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
09:48:44.611 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
09:48:44.612 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
09:48:44.708 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
09:48:44.717 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
09:48:44.721 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
09:48:44.722 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
09:48:44.722 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
09:48:44.722 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
09:48:44.723 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
09:48:44.723 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
09:48:44.723 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
09:48:44.725 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
09:48:44.779 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:48:44.796 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.83 seconds (JVM running for 3.162)
09:48:45.697 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:49:07.577 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:49:07.577 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:49:22.532 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:49:22.533 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:50:47.715 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:50:47.715 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:50:55.424 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:50:55.425 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:51:03.026 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
09:51:03.027 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:56:44.174 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
09:56:44.175 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:56:44.177 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
09:56:44.177 [http-nio-9550-exec-5] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:57:17.751 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
09:57:17.752 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:57:17.753 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
09:57:17.753 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
09:59:16.138 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
09:59:16.139 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
09:59:16.141 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
09:59:16.141 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:00:11.958 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:00:11.958 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:00:11.960 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:00:11.960 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:00:15.405 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:00:15.405 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:00:15.407 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:00:15.407 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:09:53.348 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:09:53.349 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:09:53.351 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:09:53.351 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:11:57.479 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:11:57.479 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:11:57.481 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:11:57.481 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:16:18.790 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:16:18.791 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:16:18.794 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:16:18.794 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:30:41.390 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:30:41.391 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:30:41.392 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:30:41.393 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:30:58.995 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:30:58.996 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:30:58.997 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:30:58.998 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:31:38.598 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:31:38.598 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:31:38.600 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:31:38.600 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:31:41.772 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:31:41.772 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:31:41.774 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:31:41.775 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:32:45.272 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:32:45.272 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:32:45.276 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:32:45.277 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:32:48.308 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:32:48.309 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:32:48.311 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:32:48.312 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:44:33.803 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:44:33.804 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:44:33.805 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:44:33.805 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:45:01.402 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:45:01.403 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:45:01.404 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:45:01.405 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
10:45:09.119 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
10:45:09.119 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:45:09.120 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
10:45:09.121 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:01:07.143 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
11:01:07.144 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
11:01:07.144 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
11:01:22.631 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:01:22.632 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：825(String)
11:01:22.634 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:01:22.635 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：825(String), 0(Long), 10(Long)
11:10:14.546 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 11552 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
11:10:14.550 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:10:15.554 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
11:10:15.555 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:10:15.555 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:10:15.594 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:10:16.212 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:10:16.478 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:10:16.858 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:10:16.860 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:10:18.497 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:10:18.528 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:10:18.529 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:10:18.612 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:10:18.617 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:10:18.619 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:10:18.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:10:18.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:10:18.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:10:18.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:10:18.620 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:10:18.621 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:10:18.622 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:10:18.661 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
11:10:18.678 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.364 seconds (JVM running for 4.019)
11:10:35.572 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:27:59.723 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 33634 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
11:27:59.725 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:28:00.501 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
11:28:00.502 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:28:00.502 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:28:00.533 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:28:00.975 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:28:01.177 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:28:01.459 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:28:01.459 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:28:01.726 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:28:01.748 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:28:01.749 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:28:01.810 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:28:01.814 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:28:01.816 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:28:01.816 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:28:01.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:28:01.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:28:01.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:28:01.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:28:01.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:28:01.818 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:28:01.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
11:28:01.968 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
11:28:01.971 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
11:28:01.973 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
11:28:01.974 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:28:01.977 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
11:28:01.977 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
11:28:17.854 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 34278 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
11:28:17.856 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
11:28:18.580 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
11:28:18.581 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:28:18.581 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
11:28:18.611 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:28:19.051 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
11:28:19.244 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
11:28:19.546 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
11:28:19.547 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
11:28:19.790 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
11:28:19.813 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
11:28:19.814 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
11:28:19.874 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
11:28:19.878 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
11:28:19.880 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
11:28:19.880 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
11:28:19.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
11:28:19.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
11:28:19.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
11:28:19.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
11:28:19.881 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
11:28:19.883 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
11:28:19.917 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
11:28:19.930 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.457 seconds (JVM running for 2.804)
11:28:21.274 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:28:43.781 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
11:28:43.783 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
11:28:43.784 [http-nio-9550-exec-7] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
11:29:47.676 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
11:29:47.678 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
11:29:47.678 [http-nio-9550-exec-2] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:21:34.439 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:21:34.441 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:21:34.441 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:35:50.441 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:35:50.442 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:35:50.443 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:37:16.504 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:37:16.506 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:37:16.507 [http-nio-9550-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:37:20.447 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:37:20.450 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:37:20.450 [http-nio-9550-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:38:32.666 [http-nio-9550-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:38:32.668 [http-nio-9550-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:38:32.669 [http-nio-9550-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:40:08.335 [http-nio-9550-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:40:08.337 [http-nio-9550-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:40:08.338 [http-nio-9550-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:43:38.405 [http-nio-9550-exec-10] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,31] - 执行SQL：insert into drl_work_check_records(result,check_user_name,check_name,check_content,car_id,check_time) values (?,?,?,?,?,?)
12:43:38.405 [http-nio-9550-exec-10] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,36] - SQL参数：测试(String), 打的(String), 打打打(String), 1.去除浮漆
$\sqrt{x}$
hello
h(String), 825(String), Mon Aug 25 12:43:38 CST 2025(Date)
12:43:45.653 [http-nio-9550-exec-2] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,31] - 执行SQL：insert into drl_work_check_records(result,check_user_name,check_name,check_content,car_id) values (?,?,?,?,?)
12:43:45.653 [http-nio-9550-exec-2] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,36] - SQL参数：测试(String), 打的(String), 打打打(String), 1.去除浮漆
$\sqrt{x}$
hello
h(String), 825(String)
12:43:56.767 [http-nio-9550-exec-4] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,31] - 执行SQL：insert into drl_work_check_records(result,check_user_name,check_name,check_content,car_id,check_time) values (?,?,?,?,?,?)
12:43:56.767 [http-nio-9550-exec-4] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,36] - SQL参数：测试(String), 打的(String), 打打打(String), 1.去除浮漆
$\sqrt{x}$
hello
h(String), 825(String), Mon Aug 25 12:43:56 CST 2025(Date)
12:44:21.750 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:44:21.750 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
12:44:21.751 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:44:21.751 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:44:23.470 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:44:23.471 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
12:44:23.472 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:44:23.472 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:44:27.975 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:44:27.976 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0825(String)
12:44:27.977 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:44:27.977 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0825(String), 0(Long), 10(Long)
12:45:02.430 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:45:02.431 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0825(String)
12:45:02.431 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:45:02.432 [http-nio-9550-exec-9] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0825(String), 0(Long), 10(Long)
12:54:06.199 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:54:06.199 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
12:54:06.200 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:54:06.200 [http-nio-9550-exec-8] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:54:25.449 [http-nio-9550-exec-2] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,31] - 执行SQL：insert into drl_work_check_records(result,check_user_name,check_name,check_content,car_id,check_time) values (?,?,?,?,?,?)
12:54:25.449 [http-nio-9550-exec-2] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,36] - SQL参数：测试(String), 打的(String), 打打d打(String), 1.去除浮漆
$\sqrt{x}$
hello
h(String), 822(String), Mon Aug 25 12:54:25 CST 2025(Date)
12:55:23.687 [http-nio-9550-exec-7] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,31] - 执行SQL：insert into drl_work_check_records(result,check_user_name,check_name,check_content,car_id,check_time) values (?,?,?,?,?,?)
12:55:23.688 [http-nio-9550-exec-7] INFO  /检验卡片/保存编辑(/check/saveOrUpdate) - [handleLog,36] - SQL参数：测试(String), 打的(String), 打打d打(String), 2.ssa去除浮漆
$\sqrt{x}$
hello
h(String), 822(String), Mon Aug 25 12:55:23 CST 2025(Date)
12:55:55.522 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
 ) count_
12:55:55.523 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records  order by sort
 limit ?,?
12:55:55.523 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0(Long), 10(Long)
12:56:04.579 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select count(1) from ( 
select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
 ) count_
12:56:04.579 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
12:56:04.580 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
 limit ?,?
12:56:04.581 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/record/getCheckRecord) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:56:38.997 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:56:38.997 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:56:39.000 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:56:39.001 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
13:03:45.755 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:03:45.759 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:30:51.317 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 268089 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:30:51.319 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:30:52.192 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:30:52.192 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:30:52.192 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:30:52.227 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:30:52.726 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:30:52.946 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:30:53.261 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:30:53.262 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:30:53.508 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:30:53.534 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:30:53.535 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:30:53.598 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:30:53.603 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:30:53.605 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:30:53.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:30:53.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:30:53.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:30:53.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:30:53.606 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:30:53.607 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:30:53.608 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:30:53.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:30:53.660 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.763 seconds (JVM running for 3.154)
14:36:02.126 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:41.016 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,41] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 4
14:36:41.016 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
14:36:41.016 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,53] - 导出当前页面，车辆ID: 0822, 页面顺序: 4
14:36:41.019 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
14:36:41.019 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
14:36:41.023 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:36:41.188 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:36:41.189 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 9, 总列数: 8
14:36:41.237 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:36:41.283 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:36:41.286 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:36:41.288 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:36:41.289 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:36:41.290 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:36:41.291 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
14:36:41.293 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:36:41.294 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:36:41.295 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:36:41.297 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:36:41.297 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:36:41.298 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:36:41.298 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:36:41.299 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:36:41.299 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:36:41.300 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:36:41.300 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:36:41.357 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3190 bytes
14:36:41.361 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,69] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC4%E9%A1%B5_20250825_143641.docx, 大小: 3190 bytes
14:45:11.377 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:45:11.379 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:45:15.120 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 285762 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:45:15.122 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:45:15.823 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:45:15.823 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:45:15.824 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:45:15.854 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:45:16.326 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:45:16.520 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:45:16.778 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
14:45:16.779 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
14:45:18.470 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
14:45:18.490 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
14:45:18.491 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
14:45:18.552 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
14:45:18.556 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
14:45:18.558 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
14:45:18.558 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
14:45:18.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
14:45:18.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
14:45:18.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
14:45:18.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
14:45:18.559 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
14:45:18.560 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
14:45:18.599 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:45:18.613 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.338 seconds (JVM running for 2.666)
14:45:22.883 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:45:22.907 [http-nio-9550-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,41] - 接收到导出当前页请求，车辆ID: 0822, 页面顺序: 4
14:45:22.908 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: current_page
14:45:22.908 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCurrentPage,53] - 导出当前页面，车辆ID: 0822, 页面顺序: 4
14:45:22.912 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
14:45:22.912 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
14:45:22.916 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:45:23.046 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:45:23.046 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 9, 总列数: 8
14:45:23.083 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:45:23.129 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:45:23.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:45:23.134 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:45:23.136 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:45:23.138 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:45:23.139 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:45:23.140 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:45:23.367 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
14:45:23.368 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:45:23.368 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:45:23.430 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:45:23.431 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:45:23.431 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:45:23.484 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:45:23.485 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:45:23.487 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:45:23.487 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:45:23.488 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:45:23.488 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:45:23.488 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:45:23.489 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:45:23.489 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:45:23.490 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:45:23.525 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3302 bytes
14:45:23.530 [http-nio-9550-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportCurrentPage,69] - 检验记录当前页导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E7%AC%AC4%E9%A1%B5_20250825_144523.docx, 大小: 3302 bytes
14:49:48.150 [http-nio-9550-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0822
14:49:48.151 [http-nio-9550-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
14:49:48.151 [http-nio-9550-exec-2] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0822
14:49:48.155 [http-nio-9550-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,149] - 查询检验记录数据，车辆ID: 0822
14:49:48.155 [http-nio-9550-exec-2] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,200] - 查询到检验记录数据 4 条
14:49:48.157 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1572] - 开始导出新JSON格式Word文档，表格标题: 检验记录表
14:49:48.159 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,770] - 已设置文档为横向纸张
14:49:48.159 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1616] - 创建新JSON格式表格，总行数: 19, 总列数: 8
14:49:48.163 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1687] - 设置表格总宽度: 880px (13200twips)
14:49:48.166 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:49:48.167 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:49:48.168 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:49:48.169 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 30px (450twips)
14:49:48.170 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 151px (2265twips)
14:49:48.170 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:49:48.170 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:49:48.230 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.231 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.232 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.232 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.233 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
14:49:48.234 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.234 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.235 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.235 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 51px (765twips)
14:49:48.236 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:49:48.236 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:49:48.237 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:49:48.295 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 120px (1800twips)
14:49:48.296 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:49:48.296 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:49:48.348 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1751] - 设置数据行高度: 79px (1185twips)
14:49:48.348 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,635] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:49:48.348 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:49:48.427 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1793] - 应用JSON格式表头合并单元格，数量: 7
14:49:48.428 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
14:49:48.429 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
14:49:48.429 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
14:49:48.430 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
14:49:48.430 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,982] - 应用水平合并: 行0, 列3-4, 跨度2列
14:49:48.430 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1023] - 水平合并完成: 行0, 列3-4
14:49:48.431 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
14:49:48.431 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
14:49:48.432 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1834] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
14:49:48.441 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1593] - 新JSON格式Word文档导出完成，文件大小: 3527 bytes
14:49:48.444 [http-nio-9550-exec-2] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250825_144948.docx, 大小: 3527 bytes
15:13:31.197 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:13:31.199 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:13:35.233 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 319619 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:13:35.235 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:13:36.010 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:13:36.011 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:13:36.011 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:13:36.044 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:13:36.489 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:13:36.687 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:13:36.984 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
15:13:36.985 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
15:13:37.213 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
15:13:37.237 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
15:13:37.238 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
15:13:37.297 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
15:13:37.301 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
15:13:37.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
15:13:37.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
15:13:37.303 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
15:13:37.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
15:13:37.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
15:13:37.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
15:13:37.304 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
15:13:37.305 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
15:13:37.339 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:13:37.353 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.465 seconds (JVM running for 2.803)
15:13:42.256 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:13:42.281 [http-nio-9550-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0822
15:13:42.281 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0822, 导出类型: all_pages
15:13:42.281 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0822
15:13:42.286 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,146] - 查询检验记录数据，车辆ID: 0822
15:13:42.287 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,197] - 查询到检验记录数据 4 条
15:13:42.287 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,741] - 开始导出多页面文档，页面数量: 4
15:13:42.289 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第1页数据，页面名称: 第1页
15:13:42.290 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面1构建了9行数据
15:13:42.290 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第2页数据，页面名称: 第2页
15:13:42.290 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面2构建了5行数据
15:13:42.290 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第3页数据，页面名称: 第3页
15:13:42.291 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面3构建了8行数据
15:13:42.291 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第4页数据，页面名称: 第4页
15:13:42.291 [http-nio-9550-exec-1] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面4构建了7行数据
15:13:42.293 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2008] - 开始导出支持分页符的JSON格式Word文档，表格标题: 检验记录表
15:13:42.431 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:13:42.465 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2042] - 创建支持分页符的JSON格式表格，表头行数: 2, 总列数: 8
15:13:42.465 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [splitRowsByPageBreaks,2120] - 数据行按分页符分割完成，共4页
15:13:42.506 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:13:42.539 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.542 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.546 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 151px (2265twips)
15:13:42.548 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:13:42.551 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:13:42.777 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.779 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.780 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.782 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.783 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:13:42.783 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:13:42.786 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:13:42.786 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:13:42.787 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:13:42.787 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:13:42.788 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:13:42.788 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:13:42.789 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:13:42.790 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:13:42.791 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第1页表格创建完成，数据行数: 9
15:13:42.795 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:13:42.799 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.800 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.802 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.803 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.804 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 120px (1800twips)
15:13:42.805 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:13:42.805 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:13:42.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:13:42.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:13:42.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:13:42.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:13:42.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:13:42.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:13:42.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:13:42.808 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:13:42.808 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第2页表格创建完成，数据行数: 5
15:13:42.809 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:13:42.811 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.812 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.813 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.814 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.814 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.815 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.815 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.816 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 51px (765twips)
15:13:42.816 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:13:42.817 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:13:42.817 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:13:42.817 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:13:42.817 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:13:42.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:13:42.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:13:42.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:13:42.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:13:42.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:13:42.819 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第3页表格创建完成，数据行数: 8
15:13:42.820 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:13:42.821 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.822 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.822 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 30px (450twips)
15:13:42.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 79px (1185twips)
15:13:42.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:13:42.824 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:13:42.904 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 120px (1800twips)
15:13:42.904 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:13:42.905 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:13:42.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 79px (1185twips)
15:13:42.985 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 2.ssa去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:13:42.986 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:13:43.049 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:13:43.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:13:43.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:13:43.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:13:43.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:13:43.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:13:43.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:13:43.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:13:43.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:13:43.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:13:43.053 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第4页表格创建完成，数据行数: 7
15:13:43.109 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2029] - 支持分页符的JSON格式Word文档导出完成，文件大小: 4181 bytes
15:13:43.114 [http-nio-9550-exec-1] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250825_151343.docx, 大小: 4181 bytes
15:16:29.529 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
15:16:29.529 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
15:16:29.532 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
15:16:29.532 [http-nio-9550-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
15:16:36.216 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
15:16:36.216 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0825(String)
15:16:36.217 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
15:16:36.218 [http-nio-9550-exec-9] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0825(String), 0(Long), 10(Long)
15:17:14.639 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,91] - 接收到导出全部页面请求，车辆ID: 0825
15:17:14.639 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportCheckRecord,34] - 开始导出检验记录，车辆ID: 0825, 导出类型: all_pages
15:17:14.639 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportAllPages,82] - 导出全部页面，车辆ID: 0825
15:17:14.642 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,146] - 查询检验记录数据，车辆ID: 0825
15:17:14.642 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [getCheckRecordData,197] - 查询到检验记录数据 0 条
15:17:14.642 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,741] - 开始导出多页面文档，页面数量: 2
15:17:14.643 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第1页数据，页面名称: 第1页
15:17:14.643 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面1构建了1行数据
15:17:14.643 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [exportMultiPageDocument,762] - 处理第2页数据，页面名称: 第2页
15:17:14.643 [http-nio-9550-exec-3] INFO  c.l.w.s.CheckRecordExportService - [buildCellRowsForSinglePage,666] - 页面2构建了2行数据
15:17:14.644 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2008] - 开始导出支持分页符的JSON格式Word文档，表格标题: 检验记录表
15:17:14.645 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,771] - 已设置文档为横向纸张
15:17:14.646 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2042] - 创建支持分页符的JSON格式表格，表头行数: 2, 总列数: 8
15:17:14.646 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [splitRowsByPageBreaks,2120] - 数据行按分页符分割完成，共2页
15:17:14.646 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:17:14.648 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 300px (4500twips)
15:17:14.648 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:17:14.648 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:17:14.706 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:17:14.706 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:17:14.706 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:17:14.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:17:14.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:17:14.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:17:14.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:17:14.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:17:14.708 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:17:14.708 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:17:14.708 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第1页表格创建完成，数据行数: 1
15:17:14.708 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1688] - 设置表格总宽度: 880px (13200twips)
15:17:14.709 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 138px (2070twips)
15:17:14.710 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:17:14.737 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:17:14.797 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1752] - 设置数据行高度: 79px (1185twips)
15:17:14.798 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,636] - 插入混合内容，内容: 1.去除浮漆
__MATH_FORMULA_0__
hello
h, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:17:14.798 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:17:14.853 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1794] - 应用JSON格式表头合并单元格，数量: 7
15:17:14.853 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,983] - 应用水平合并: 行0, 列3-4, 跨度2列
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHorizontalMerge,1024] - 水平合并完成: 行0, 列3-4
15:17:14.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
15:17:14.855 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
15:17:14.855 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1835] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
15:17:14.856 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormatWithPageBreaks,2083] - 第2页表格创建完成，数据行数: 2
15:17:14.861 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWordWithPageBreaks,2029] - 支持分页符的JSON格式Word文档导出完成，文件大小: 3297 bytes
15:17:14.865 [http-nio-9550-exec-3] INFO  c.l.w.c.CheckRecordExportController - [exportAllPages,114] - 检验记录全部页面导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_%E5%85%A8%E9%83%A8%E9%A1%B5%E9%9D%A2_20250825_151714.docx, 大小: 3297 bytes
21:16:23.927 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 9490 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:16:23.931 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:16:24.786 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:16:24.787 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:16:24.787 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:16:24.823 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:16:25.360 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:16:25.589 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:16:25.890 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
21:16:25.891 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
21:16:26.188 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
21:16:26.216 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
21:16:26.217 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
21:16:26.285 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
21:16:26.291 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
21:16:26.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
21:16:26.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
21:16:26.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
21:16:26.294 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
21:16:26.295 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
21:16:26.295 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
21:16:26.295 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
21:16:26.297 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
21:16:26.334 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:16:26.349 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.858 seconds (JVM running for 3.43)
21:16:51.131 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:12:55.490 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:12:55.495 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:18:41.926 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 152981 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:18:41.928 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:18:42.783 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:18:42.784 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:18:42.784 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:18:42.820 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:18:43.341 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:18:43.560 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:18:43.871 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:18:43.872 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:18:44.163 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:18:44.186 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:18:44.187 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:18:44.259 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:18:44.264 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:18:44.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:18:44.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:18:44.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:18:44.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:18:44.267 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:18:44.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:18:44.268 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:18:44.270 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:18:44.311 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:18:44.327 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.778 seconds (JVM running for 3.167)
23:21:18.270 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:21:18.325 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: 表格导出
23:21:18.325 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 0, 数据合并数量: 0
23:21:18.325 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
23:21:18.552 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:21:18.552 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1618] - 创建新JSON格式表格，总行数: 2, 总列数: 4
23:21:18.596 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1689] - 设置表格总宽度: 570px (8550twips)
23:21:18.601 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1753] - 设置数据行高度: 51px (765twips)
23:21:18.629 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1753] - 设置数据行高度: 51px (765twips)
23:21:18.698 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2640 bytes
23:21:18.705 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250825_232118.docx, 大小: 2640 bytes
23:22:05.885 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
23:22:05.885 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
23:22:05.886 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
23:22:05.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:22:05.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1618] - 创建新JSON格式表格，总行数: 1, 总列数: 3
23:22:05.889 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1689] - 设置表格总宽度: 550px (8250twips)
23:22:05.889 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1753] - 设置数据行高度: 197px (2955twips)
23:22:05.890 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1800] - 开始处理嵌套表格，父单元格内容: 包含子项目详情
23:22:05.896 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createNestedTable,1850] - 创建嵌套表格: 2行 x 3列
23:22:05.907 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2580 bytes
23:22:05.910 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250825_232205.docx, 大小: 2580 bytes
23:29:09.647 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:29:09.649 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:29:14.473 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 166479 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:29:14.475 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:29:15.250 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:29:15.251 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:29:15.251 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:29:15.283 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:29:15.731 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:29:15.941 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:29:16.212 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
23:29:16.213 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
23:29:16.471 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
23:29:16.493 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
23:29:16.494 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
23:29:16.563 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
23:29:16.568 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
23:29:16.571 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
23:29:16.571 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
23:29:16.572 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
23:29:16.572 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
23:29:16.572 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
23:29:16.572 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
23:29:16.573 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
23:29:16.575 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
23:29:16.612 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:29:16.626 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.494 seconds (JVM running for 2.899)
23:29:21.870 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:29:33.485 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: 表格导出
23:29:33.486 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 0, 数据合并数量: 0
23:29:33.486 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
23:29:33.674 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:29:33.675 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1618] - 创建新JSON格式表格，总行数: 2, 总列数: 4
23:29:33.760 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1710] - 设置表格总宽度: 570px (8550twips)
23:29:33.774 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1790] - 设置数据行高度: 51px (765twips)
23:29:33.847 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1790] - 设置数据行高度: 51px (765twips)
23:29:33.973 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2640 bytes
23:29:33.985 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,99] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250825_232933.docx, 大小: 2640 bytes
23:30:03.805 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [previewTable,171] - 接收到表格预览请求，标题: 表格导出
23:30:03.806 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,363] - JSON请求转换完成，表头数量: 0, 数据行数量: 2, 合并单元格数量: 0
23:30:03.807 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [previewTable,179] - 表格预览生成成功
23:30:27.729 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
23:30:27.730 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
23:30:27.730 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
23:30:27.732 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:30:27.732 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1618] - 创建新JSON格式表格，总行数: 1, 总列数: 8
23:30:27.734 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1710] - 设置表格总宽度: 890px (13350twips)
23:30:27.734 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1790] - 设置数据行高度: 197px (2955twips)
23:30:27.735 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1837] - 开始处理嵌套表格，父单元格内容: 详细技术要求
23:30:27.739 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createNestedTable,1905] - 创建嵌套表格: 2行 x 3列
23:30:27.742 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 嵌套表格处理完成
23:30:27.752 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2637 bytes
23:30:27.755 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250825_233027.docx, 大小: 2637 bytes
23:31:04.073 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportComplexTable,121] - 接收到复杂表格导出请求，标题: 表格导出
23:31:04.073 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportComplexTable,126] - 嵌套表格统计: 总数=1, 最大层级=1, 总单元格数=6
23:31:04.074 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1574] - 开始导出新JSON格式Word文档，表格标题: 表格导出
23:31:04.075 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,772] - 已设置文档为横向纸张
23:31:04.075 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1618] - 创建新JSON格式表格，总行数: 1, 总列数: 8
23:31:04.077 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1710] - 设置表格总宽度: 890px (13350twips)
23:31:04.077 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRowFromJsonCellRows,1790] - 设置数据行高度: 197px (2955twips)
23:31:04.078 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1837] - 开始处理嵌套表格，父单元格内容: 详细技术要求
23:31:04.079 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createNestedTable,1905] - 创建嵌套表格: 2行 x 3列
23:31:04.079 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processNestedTableInCell,1883] - 嵌套表格处理完成
23:31:04.085 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1595] - 新JSON格式Word文档导出完成，文件大小: 2637 bytes
23:31:04.089 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportComplexTable,145] - 复杂表格Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_%E5%A4%8D%E6%9D%82%E8%A1%A8%E6%A0%BC_20250825_233104.docx, 大小: 2637 bytes
