import request from '@/utils/request'

/**
 * Word导出相关API
 */

/**
 * 导出表格到Word文档（支持合并单元格）
 * @param {Object} data 表格数据
 * @param {string} data.title - 文档标题
 * @param {Array} data.headers - 表头数组
 * @param {Array} data.cellRows - 数据行数组（支持公式和宽度配置）
 * @param {Array} data.merges - 合并单元格配置数组
 * @param {Object} data.metadata - 元数据信息
 * @returns {Promise} 返回文件流
 */
export function exportTableToWord(data) {
  console.log('调用Word导出API，数据:', data)

  // 检查是否包含嵌套表格
  const hasNestedTables = checkForNestedTables(data)
  const apiUrl = hasNestedTables ? '/word/exportComplexTable' : '/word/exportNewJsonFormat'

  console.log('检测到嵌套表格:', hasNestedTables, '使用API:', apiUrl)

  return request({
    url: apiUrl,
    method: 'post',
    data: data,
    responseType: 'blob', // 重要：设置响应类型为blob以处理文件流
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: hasNestedTables ? 120000 : 60000 // 嵌套表格需要更长的处理时间
  })
}

/**
 * 导出复杂表格到Word文档（包含嵌套表格）
 * @param {Object} data 表格数据
 * @param {string} data.title - 文档标题
 * @param {Array} data.headers - 表头数组
 * @param {Array} data.cellRows - 数据行数组（支持嵌套表格）
 * @param {Array} data.merges - 合并单元格配置数组
 * @param {Object} data.metadata - 元数据信息
 * @returns {Promise} 返回文件流
 */
export function exportComplexTableToWord(data) {
  console.log('调用复杂表格Word导出API，数据:', data)

  return request({
    url: '/word/exportComplexTable',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 120000 // 复杂表格需要更长的处理时间
  })
}

/**
 * 导出简单表格到Word文档（兼容旧版本，不含合并单元格）
 * @param {Object} data 表格数据
 * @returns {Promise} 返回文件流
 */
export function exportSimpleTableToWord(data) {
  return request({
    url: '/word/exportTable',
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: 60000
  })
}

/**
 * 预览Word导出效果（返回HTML预览）
 * @param {Object} data 导出数据
 * @returns {Promise} 返回HTML预览内容
 */
export function previewWordExport(data) {
  console.log('预览Word导出效果，数据:', data)

  return request({
    url: '/word/previewTable',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取Word导出模板列表
 * @returns {Promise} 返回模板列表
 */
export function getWordTemplates() {
  return request({
    url: '/word/templates',
    method: 'get'
  })
}

/**
 * 导出检验记录当前页
 * @param {Object} data 导出请求数据
 * @param {string} data.carId - 车辆ID
 * @param {number} data.pageOrder - 页面顺序
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordCurrentPage(data) {
  console.log('调用检验记录当前页导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportCurrentPage',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 120000 // 设置2分钟超时，因为后端查询数据可能需要较长时间
  })
}

/**
 * 导出检验记录全部页面
 * @param {Object} data 导出请求数据
 * @param {string} data.carId - 车辆ID
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordAllPages(data) {
  console.log('调用检验记录全部页面导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportAllPages',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 180000 // 设置3分钟超时，因为全部页面导出可能需要更长时间
  })
}

/**
 * 批量导出检验记录
 * @param {Object} data 导出请求数据
 * @param {Array} data.carIds - 车辆ID列表
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordBatch(data) {
  console.log('调用检验记录批量导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportBatch',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 300000 // 设置5分钟超时，因为批量导出可能需要很长时间
  })
}

/**
 * 使用指定模板导出Word文档
 * @param {Object} params 导出参数
 * @param {string} params.templateId - 模板ID
 * @param {Object} params.data - 数据
 * @returns {Promise} 返回文件流
 */
export function exportWithTemplate(params) {
  console.log('使用模板导出Word，参数:', params)

  return request({
    url: '/word/exportWithTemplate',
    method: 'post',
    data: params,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 60000
  })
}

/**
 * 检查数据中是否包含嵌套表格
 * @param {Object} data 表格数据
 * @returns {boolean} 是否包含嵌套表格
 */
function checkForNestedTables(data) {
  if (!data.cellRows || !Array.isArray(data.cellRows)) {
    return false
  }

  for (const row of data.cellRows) {
    if (!Array.isArray(row)) continue

    for (const cell of row) {
      if (cell.nestedTable && cell.nestedTable.enabled === true) {
        return true
      }
    }
  }

  return false
}

/**
 * 分析嵌套表格统计信息
 * @param {Object} data 表格数据
 * @returns {Object} 统计信息
 */
export function analyzeNestedTables(data) {
  const stats = {
    totalNestedTables: 0,
    maxNestedLevel: 0,
    totalNestedCells: 0,
    nestedTablePositions: []
  }

  if (!data.cellRows || !Array.isArray(data.cellRows)) {
    return stats
  }

  data.cellRows.forEach((row, rowIndex) => {
    if (!Array.isArray(row)) return

    row.forEach((cell, cellIndex) => {
      if (cell.nestedTable && cell.nestedTable.enabled === true) {
        stats.totalNestedTables++
        stats.nestedTablePositions.push({ row: rowIndex, cell: cellIndex })

        // 分析嵌套层级
        const nestedLevel = analyzeNestedLevel(cell.nestedTable, 1)
        stats.maxNestedLevel = Math.max(stats.maxNestedLevel, nestedLevel)

        // 统计嵌套单元格数
        const nestedCells = countNestedCells(cell.nestedTable)
        stats.totalNestedCells += nestedCells
      }
    })
  })

  return stats
}

/**
 * 分析嵌套层级
 * @param {Object} nestedTable 嵌套表格配置
 * @param {number} currentLevel 当前层级
 * @returns {number} 最大层级
 */
function analyzeNestedLevel(nestedTable, currentLevel) {
  if (!nestedTable.config || !nestedTable.config.cellRows) {
    return currentLevel
  }

  let maxLevel = currentLevel

  nestedTable.config.cellRows.forEach(row => {
    if (!Array.isArray(row)) return

    row.forEach(cell => {
      if (cell.nestedTable && cell.nestedTable.enabled === true) {
        const nestedLevel = analyzeNestedLevel(cell.nestedTable, currentLevel + 1)
        maxLevel = Math.max(maxLevel, nestedLevel)
      }
    })
  })

  return maxLevel
}

/**
 * 统计嵌套单元格数
 * @param {Object} nestedTable 嵌套表格配置
 * @returns {number} 单元格数量
 */
function countNestedCells(nestedTable) {
  if (!nestedTable.config || !nestedTable.config.cellRows) {
    return 0
  }

  let totalCells = 0

  nestedTable.config.cellRows.forEach(row => {
    if (!Array.isArray(row)) return

    totalCells += row.length

    // 递归统计更深层的嵌套表格
    row.forEach(cell => {
      if (cell.nestedTable && cell.nestedTable.enabled === true) {
        totalCells += countNestedCells(cell.nestedTable)
      }
    })
  })

  return totalCells
}

/**
 * 验证嵌套表格数据结构
 * @param {Object} data 表格数据
 * @returns {Object} 验证结果
 */
export function validateNestedTableData(data) {
  const result = {
    isValid: true,
    errors: [],
    warnings: []
  }

  if (!data.cellRows || !Array.isArray(data.cellRows)) {
    result.isValid = false
    result.errors.push('表格数据不能为空')
    return result
  }

  const stats = analyzeNestedTables(data)

  // 检查嵌套层级
  if (stats.maxNestedLevel > 3) {
    result.isValid = false
    result.errors.push(`嵌套层级过深(${stats.maxNestedLevel}层)，最大支持3层嵌套`)
  }

  // 检查嵌套表格数量
  if (stats.totalNestedTables > 50) {
    result.isValid = false
    result.errors.push(`嵌套表格数量过多(${stats.totalNestedTables}个)，最大支持50个`)
  } else if (stats.totalNestedTables > 20) {
    result.warnings.push(`嵌套表格数量较多(${stats.totalNestedTables}个)，可能影响导出性能`)
  }

  // 检查嵌套单元格总数
  if (stats.totalNestedCells > 1000) {
    result.isValid = false
    result.errors.push(`嵌套单元格总数过多(${stats.totalNestedCells}个)，最大支持1000个`)
  } else if (stats.totalNestedCells > 500) {
    result.warnings.push(`嵌套单元格数量较多(${stats.totalNestedCells}个)，可能影响导出性能`)
  }

  return result
}


