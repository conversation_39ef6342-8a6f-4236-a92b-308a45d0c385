package com.logictrue.word.controller;

import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.dto.TableExportRequest;
import com.logictrue.word.service.WordExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Word导出控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WordExportController {

    private final WordExportService wordExportService;

    /**
     * 导出表格到Word文档
     */
    @PostMapping("/exportTable")
    public ResponseEntity<byte[]> exportTable(@RequestBody TableExportRequest request) {
        try {
            log.info("接收到表格导出请求，标题: {}", request.getTitle());

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("表格导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出Word文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 导出新JSON格式的表格到Word文档
     * 处理示例JSON数据格式，参考SimpleWordService的实现
     */
    @PostMapping("/exportNewJsonFormat")
    public ResponseEntity<byte[]> exportNewJsonFormat(@RequestBody JsonTableExportRequest jsonRequest) {
        try {
            log.info("接收到新JSON格式的表格导出请求，标题: {}",
                    jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle());
            log.info("表头合并数量: {}, 数据合并数量: {}",
                    jsonRequest.getHeaderMerges() != null ? jsonRequest.getHeaderMerges().size() : 0,
                    jsonRequest.getMerges() != null ? jsonRequest.getMerges().size() : 0);

            // 检查是否包含嵌套表格
            boolean hasNestedTables = checkForNestedTables(jsonRequest);
            if (hasNestedTables) {
                log.info("检测到嵌套表格，使用增强导出模式");
            }

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportNewJsonFormatToWord(jsonRequest);

            // 生成文件名
            String title = jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle();
            String fileName = generateFileName(title);

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("新JSON格式Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);

        } catch (IOException e) {
            log.error("新JSON格式导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理新JSON格式导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 导出包含嵌套表格的复杂表格到Word文档
     */
    @PostMapping("/exportComplexTable")
    public ResponseEntity<byte[]> exportComplexTable(@RequestBody JsonTableExportRequest jsonRequest) {
        try {
            log.info("接收到复杂表格导出请求，标题: {}",
                    jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle());

            // 统计嵌套表格信息
            NestedTableStats stats = analyzeNestedTables(jsonRequest);
            log.info("嵌套表格统计: 总数={}, 最大层级={}, 总单元格数={}",
                    stats.getTotalNestedTables(), stats.getMaxNestedLevel(), stats.getTotalNestedCells());

            // 验证复杂表格结构
            validateComplexTableStructure(jsonRequest);

            // 导出Word文档（使用增强模式）
            byte[] wordBytes = wordExportService.exportNewJsonFormatToWord(jsonRequest);

            // 生成文件名
            String title = jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle();
            String fileName = generateFileName(title + "_复杂表格");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("复杂表格Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(wordBytes);

        } catch (IllegalArgumentException e) {
            log.error("复杂表格结构验证失败", e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(("结构验证失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            log.error("复杂表格导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理复杂表格导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 预览表格导出效果（返回HTML预览）
     */
    @PostMapping("/previewTable")
    public ResponseEntity<String> previewTable(@RequestBody JsonTableExportRequest jsonRequest) {
        try {
            log.info("接收到表格预览请求，标题: {}", jsonRequest.getTitle());

            // 转换JSON格式的请求为内部格式
            TableExportRequest request = convertJsonRequestToTableRequest(jsonRequest);

            // 生成HTML预览
            String htmlPreview = wordExportService.generateTablePreview(request);

            log.info("表格预览生成成功");

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlPreview);

        } catch (Exception e) {
            log.error("生成表格预览失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("<html><body><h1>预览生成失败</h1><p>" + e.getMessage() + "</p></body></html>");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String title) {
        try {
            String baseFileName = (title != null && !title.trim().isEmpty())
                    ? title.trim()
                    : "表格导出";

            // 添加时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = baseFileName + "_" + timestamp + ".docx";

            // URL编码文件名以支持中文
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名", e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            return "table_export_" + timestamp + ".docx";
        }
    }


    /**
     * 将JSON格式的请求转换为内部TableExportRequest格式
     */
    private TableExportRequest convertJsonRequestToTableRequest(JsonTableExportRequest jsonRequest) {
        TableExportRequest request = new TableExportRequest();

        // 设置基本信息
        request.setTitle(jsonRequest.getTitle());
        request.setTableWidth(1200); // 默认宽度
        request.setTableHeight(600);  // 默认高度
        request.setPageOrientation("LANDSCAPE"); // 默认横向

        // 转换数据行合并单元格信息
        if (jsonRequest.getMerges() != null && !jsonRequest.getMerges().isEmpty()) {
            List<TableExportRequest.MergeCell> merges = new ArrayList<>();
            for (JsonTableExportRequest.MergeConfig mergeConfig : jsonRequest.getMerges()) {
                TableExportRequest.MergeCell mergeCell = new TableExportRequest.MergeCell();
                mergeCell.setStartRow(mergeConfig.getStartRow());
                mergeCell.setStartCol(mergeConfig.getStartCol());
                mergeCell.setEndRow(mergeConfig.getEndRow());
                mergeCell.setEndCol(mergeConfig.getEndCol());
                mergeCell.setContent(mergeConfig.getContent());
                merges.add(mergeCell);
            }
            request.setMerges(merges);
        }

        // 转换表头合并单元格信息
        if (jsonRequest.getHeaderMerges() != null && !jsonRequest.getHeaderMerges().isEmpty()) {
            List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
            for (JsonTableExportRequest.MergeConfig mergeConfig : jsonRequest.getHeaderMerges()) {
                TableExportRequest.MergeCell mergeCell = new TableExportRequest.MergeCell();
                mergeCell.setStartRow(mergeConfig.getStartRow());
                mergeCell.setStartCol(mergeConfig.getStartCol());
                mergeCell.setEndRow(mergeConfig.getEndRow());
                mergeCell.setEndCol(mergeConfig.getEndCol());
                mergeCell.setContent(mergeConfig.getContent());
                headerMerges.add(mergeCell);
            }
            request.setHeaderMerges(headerMerges);
        }

        // 转换表头宽度配置
        if (jsonRequest.getHeaderWidthConfig() != null) {
            TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();

            // 转换列宽数组
            if (jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                headerWidthConfig.setColumnWidths(jsonRequest.getHeaderWidthConfig().getColumnWidths());
            }

            // 转换表头行高数组
            if (jsonRequest.getHeaderWidthConfig().getHeaderHeights() != null) {
                headerWidthConfig.setHeaderHeights(jsonRequest.getHeaderWidthConfig().getHeaderHeights());
            }

            request.setHeaderWidthConfig(headerWidthConfig);
            log.debug("转换表头宽度配置: 列宽={}, 行高={}",
                     headerWidthConfig.getColumnWidths(),
                     headerWidthConfig.getHeaderHeights());
        }

        // 转换表头纵向文字配置
        if (jsonRequest.getVerticalHeadersConfig() != null) {
            request.setVerticalHeadersConfig(jsonRequest.getVerticalHeadersConfig());
            log.debug("转换表头纵向文字配置: {}", jsonRequest.getVerticalHeadersConfig());
        }

        // 转换元数据
        if (jsonRequest.getMetadata() != null) {
            TableExportRequest.Metadata metadata = new TableExportRequest.Metadata();
            metadata.setExportTime(jsonRequest.getMetadata().getExportTime());
            metadata.setTotalRows(jsonRequest.getMetadata().getTotalRows());
            metadata.setTotalColumns(jsonRequest.getMetadata().getTotalColumns());
            metadata.setHasMergedCells(jsonRequest.getMetadata().getHasMergedCells());
            request.setMetadata(metadata);
        }

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 转换表头（支持多行表头）
        if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
            List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

            // 遍历每一行表头
            for (int rowIndex = 0; rowIndex < jsonRequest.getHeaders().size(); rowIndex++) {
                List<String> headerRowData = jsonRequest.getHeaders().get(rowIndex);
                List<TableExportRequest.HeaderCell> headerRow = new ArrayList<>();

                // 遍历每一列
                for (int colIndex = 0; colIndex < headerRowData.size(); colIndex++) {
                    String headerContent = headerRowData.get(colIndex);
                    TableExportRequest.HeaderCell headerCell = new TableExportRequest.HeaderCell();
                    headerCell.setContent(headerContent);
                    headerCell.setWidth(150); // 默认宽度
                    headerCell.setHeight(50); // 默认高度
                    headerCell.setIsVertical(false);

                    // 检查是否为表头合并单元格，设置rowspan和colspan
                    setHeaderCellMergeInfo(headerCell, jsonRequest.getHeaderMerges(), rowIndex, colIndex);

                    headerRow.add(headerCell);
                }
                headers.add(headerRow);
            }
            tableData.setHeaders(headers);
        }

        // 转换数据行（使用cellRows格式）
        if (jsonRequest.getCellRows() != null && !jsonRequest.getCellRows().isEmpty()) {
            List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

            for (int rowIndex = 0; rowIndex < jsonRequest.getCellRows().size(); rowIndex++) {
                List<JsonTableExportRequest.CellData> jsonRow = jsonRequest.getCellRows().get(rowIndex);
                List<TableExportRequest.DataCell> dataRow = new ArrayList<>();

                for (int colIndex = 0; colIndex < jsonRow.size(); colIndex++) {
                    JsonTableExportRequest.CellData cellData = jsonRow.get(colIndex);
                    TableExportRequest.DataCell dataCell = new TableExportRequest.DataCell();

                    // 设置基本内容
                    dataCell.setContent(cellData.getContent());
                    dataCell.setHasMath(cellData.getHasMath() != null ? cellData.getHasMath() : false);

                    // 设置公式相关信息
                    dataCell.setMathML(cellData.getMathML());
                    dataCell.setHasMultipleContent(cellData.getHasMultipleContent() != null ? cellData.getHasMultipleContent() : false);
                    dataCell.setMathMLMap(cellData.getMathMLMap());

                    // 设置尺寸（使用传入的值或默认值）
                    dataCell.setWidth(cellData.getWidth() != null ? cellData.getWidth() : 150);
                    dataCell.setHeight(cellData.getHeight() != null ? cellData.getHeight() : 50);

                    // 检查是否为合并单元格，设置rowspan和colspan
                    setCellMergeInfo(dataCell, jsonRequest.getMerges(), rowIndex, colIndex);

                    dataRow.add(dataCell);
                }
                dataRows.add(dataRow);
            }
            tableData.setDataRows(dataRows);
        }

        request.setTableData(tableData);

        log.info("JSON请求转换完成，表头数量: {}, 数据行数量: {}, 合并单元格数量: {}",
                jsonRequest.getHeaders() != null ? jsonRequest.getHeaders().size() : 0,
                jsonRequest.getCellRows() != null ? jsonRequest.getCellRows().size() : 0,
                jsonRequest.getMerges() != null ? jsonRequest.getMerges().size() : 0);

        return request;
    }

    /**
     * 检查内容是否包含数学公式
     */
    private boolean containsMathFormula(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        // 简单检查是否包含LaTeX数学公式标记
        return content.contains("$") || content.contains("\\") ||
               content.contains("^") || content.contains("_");
    }

    /**
     * 为数据单元格设置合并信息
     */
    private void setCellMergeInfo(TableExportRequest.DataCell dataCell,
                                  List<JsonTableExportRequest.MergeConfig> merges,
                                  int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return;
        }

        for (JsonTableExportRequest.MergeConfig merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                // 这是合并单元格的主单元格
                int rowspan = merge.getEndRow() - merge.getStartRow() + 1;
                int colspan = merge.getEndCol() - merge.getStartCol() + 1;
                dataCell.setRowspan(rowspan);
                dataCell.setColspan(colspan);

                // 如果合并单元格有内容，使用合并单元格的内容
                if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
                    dataCell.setContent(merge.getContent());
                    dataCell.setHasMath(containsMathFormula(merge.getContent()));
                }
                break;
            }
        }
    }

    /**
     * 为表头单元格设置合并信息
     */
    private void setHeaderCellMergeInfo(TableExportRequest.HeaderCell headerCell,
                                       List<JsonTableExportRequest.MergeConfig> headerMerges,
                                       int rowIndex, int colIndex) {
        if (headerMerges == null || headerMerges.isEmpty()) {
            // 没有合并配置，设置默认值
            headerCell.setRowspan(1);
            headerCell.setColspan(1);
            return;
        }

        for (JsonTableExportRequest.MergeConfig merge : headerMerges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                // 这是合并单元格的主单元格
                int rowspan = merge.getEndRow() - merge.getStartRow() + 1;
                int colspan = merge.getEndCol() - merge.getStartCol() + 1;
                headerCell.setRowspan(rowspan);
                headerCell.setColspan(colspan);

                // 如果合并单元格有内容，使用合并单元格的内容
                if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
                    headerCell.setContent(merge.getContent());
                }

                log.debug("设置表头合并单元格: ({},{}) rowspan={} colspan={} content={}",
                         rowIndex, colIndex, rowspan, colspan, headerCell.getContent());
                return;
            }
        }

        // 检查是否被其他合并单元格覆盖
        for (JsonTableExportRequest.MergeConfig merge : headerMerges) {
            if (rowIndex >= merge.getStartRow() && rowIndex <= merge.getEndRow() &&
                colIndex >= merge.getStartCol() && colIndex <= merge.getEndCol() &&
                !(merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex))) {
                // 这个单元格被其他合并单元格覆盖，设置为隐藏
                headerCell.setRowspan(0);
                headerCell.setColspan(0);
                headerCell.setContent(""); // 清空内容
                log.debug("表头单元格被合并覆盖: ({},{})", rowIndex, colIndex);
                return;
            }
        }

        // 普通单元格，设置默认值
        headerCell.setRowspan(1);
        headerCell.setColspan(1);
    }

    /**
     * 检查是否包含嵌套表格
     */
    private boolean checkForNestedTables(JsonTableExportRequest jsonRequest) {
        if (jsonRequest.getCellRows() == null) {
            return false;
        }

        for (List<JsonTableExportRequest.CellData> row : jsonRequest.getCellRows()) {
            for (JsonTableExportRequest.CellData cell : row) {
                if (cell.getNestedTable() != null && Boolean.TRUE.equals(cell.getNestedTable().getEnabled())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 分析嵌套表格统计信息
     */
    private NestedTableStats analyzeNestedTables(JsonTableExportRequest jsonRequest) {
        NestedTableStats stats = new NestedTableStats();

        if (jsonRequest.getCellRows() == null) {
            return stats;
        }

        for (List<JsonTableExportRequest.CellData> row : jsonRequest.getCellRows()) {
            for (JsonTableExportRequest.CellData cell : row) {
                if (cell.getNestedTable() != null && Boolean.TRUE.equals(cell.getNestedTable().getEnabled())) {
                    stats.incrementTotalNestedTables();

                    // 分析嵌套层级
                    int nestedLevel = analyzeNestedLevel(cell.getNestedTable(), 1);
                    stats.updateMaxNestedLevel(nestedLevel);

                    // 统计嵌套单元格数
                    int nestedCells = countNestedCells(cell.getNestedTable());
                    stats.addNestedCells(nestedCells);
                }
            }
        }

        return stats;
    }

    /**
     * 分析嵌套层级
     */
    private int analyzeNestedLevel(JsonTableExportRequest.NestedTableConfig nestedTable, int currentLevel) {
        if (nestedTable.getConfig() == null || nestedTable.getConfig().getCellRows() == null) {
            return currentLevel;
        }

        int maxLevel = currentLevel;
        for (List<JsonTableExportRequest.CellData> row : nestedTable.getConfig().getCellRows()) {
            for (JsonTableExportRequest.CellData cell : row) {
                if (cell.getNestedTable() != null && Boolean.TRUE.equals(cell.getNestedTable().getEnabled())) {
                    int nestedLevel = analyzeNestedLevel(cell.getNestedTable(), currentLevel + 1);
                    maxLevel = Math.max(maxLevel, nestedLevel);
                }
            }
        }

        return maxLevel;
    }

    /**
     * 统计嵌套单元格数
     */
    private int countNestedCells(JsonTableExportRequest.NestedTableConfig nestedTable) {
        if (nestedTable.getConfig() == null || nestedTable.getConfig().getCellRows() == null) {
            return 0;
        }

        int totalCells = 0;
        for (List<JsonTableExportRequest.CellData> row : nestedTable.getConfig().getCellRows()) {
            totalCells += row.size();

            // 递归统计更深层的嵌套表格
            for (JsonTableExportRequest.CellData cell : row) {
                if (cell.getNestedTable() != null && Boolean.TRUE.equals(cell.getNestedTable().getEnabled())) {
                    totalCells += countNestedCells(cell.getNestedTable());
                }
            }
        }

        return totalCells;
    }

    /**
     * 验证复杂表格结构
     */
    private void validateComplexTableStructure(JsonTableExportRequest jsonRequest) {
        if (jsonRequest.getCellRows() == null || jsonRequest.getCellRows().isEmpty()) {
            throw new IllegalArgumentException("表格数据不能为空");
        }

        // 验证嵌套表格的合理性
        NestedTableStats stats = analyzeNestedTables(jsonRequest);

        if (stats.getMaxNestedLevel() > 3) {
            throw new IllegalArgumentException("嵌套层级过深，最大支持3层嵌套");
        }

        if (stats.getTotalNestedTables() > 50) {
            throw new IllegalArgumentException("嵌套表格数量过多，最大支持50个嵌套表格");
        }

        if (stats.getTotalNestedCells() > 1000) {
            throw new IllegalArgumentException("嵌套单元格总数过多，最大支持1000个单元格");
        }

        // 验证每个嵌套表格的结构
        validateNestedTableStructures(jsonRequest);
    }

    /**
     * 验证嵌套表格结构
     */
    private void validateNestedTableStructures(JsonTableExportRequest jsonRequest) {
        for (int rowIndex = 0; rowIndex < jsonRequest.getCellRows().size(); rowIndex++) {
            List<JsonTableExportRequest.CellData> row = jsonRequest.getCellRows().get(rowIndex);
            for (int cellIndex = 0; cellIndex < row.size(); cellIndex++) {
                JsonTableExportRequest.CellData cell = row.get(cellIndex);
                if (cell.getNestedTable() != null && Boolean.TRUE.equals(cell.getNestedTable().getEnabled())) {
                    validateSingleNestedTable(cell.getNestedTable(), rowIndex, cellIndex);
                }
            }
        }
    }

    /**
     * 验证单个嵌套表格
     */
    private void validateSingleNestedTable(JsonTableExportRequest.NestedTableConfig nestedTable, int parentRow, int parentCell) {
        if (nestedTable.getConfig() == null) {
            throw new IllegalArgumentException(String.format("位置(%d,%d)的嵌套表格配置为空", parentRow, parentCell));
        }

        JsonTableExportRequest.NestedTableData config = nestedTable.getConfig();
        if (config.getCellRows() == null || config.getCellRows().isEmpty()) {
            throw new IllegalArgumentException(String.format("位置(%d,%d)的嵌套表格数据为空", parentRow, parentCell));
        }

        // 验证行列一致性
        int expectedCols = config.getCellRows().get(0).size();
        for (int i = 1; i < config.getCellRows().size(); i++) {
            if (config.getCellRows().get(i).size() != expectedCols) {
                throw new IllegalArgumentException(String.format("位置(%d,%d)的嵌套表格第%d行列数不一致", parentRow, parentCell, i));
            }
        }
    }

    /**
     * 嵌套表格统计信息
     */
    private static class NestedTableStats {
        private int totalNestedTables = 0;
        private int maxNestedLevel = 0;
        private int totalNestedCells = 0;

        public void incrementTotalNestedTables() {
            this.totalNestedTables++;
        }

        public void updateMaxNestedLevel(int level) {
            this.maxNestedLevel = Math.max(this.maxNestedLevel, level);
        }

        public void addNestedCells(int cells) {
            this.totalNestedCells += cells;
        }

        public int getTotalNestedTables() {
            return totalNestedTables;
        }

        public int getMaxNestedLevel() {
            return maxNestedLevel;
        }

        public int getTotalNestedCells() {
            return totalNestedCells;
        }
    }
}
