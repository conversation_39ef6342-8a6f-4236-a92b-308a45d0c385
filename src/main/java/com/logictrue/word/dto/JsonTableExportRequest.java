package com.logictrue.word.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * JSON格式的表格导出请求DTO
 * 用于接收前端传递的JSON数据格式
 */
@Data
public class JsonTableExportRequest {

    /**
     * 文档标题
     */
    private String title;

    /**
     * 表头数组（支持多行表头）
     */
    private List<List<String>> headers;

    /**
     * 数据行数组（支持公式和宽度配置）
     */
    private List<List<CellData>> cellRows;

    /**
     * 数据行合并单元格配置数组
     */
    private List<MergeConfig> merges;

    /**
     * 表头合并单元格配置数组
     */
    private List<MergeConfig> headerMerges;

    /**
     * 表头配置（新格式）
     */
    private HeaderConfig headerConfig;

    /**
     * 表头宽度配置
     */
    private HeaderWidthConfig headerWidthConfig;

    /**
     * 表头纵向文字配置
     */
    private List<Boolean> verticalHeadersConfig;

    /**
     * 元数据信息
     */
    private MetadataInfo metadata;

    /**
     * 合并单元格配置
     */
    @Data
    public static class MergeConfig {
        /**
         * 起始行索引（从0开始）
         */
        private Integer startRow;

        /**
         * 起始列索引（从0开始）
         */
        private Integer startCol;

        /**
         * 结束行索引（从0开始）
         */
        private Integer endRow;

        /**
         * 结束列索引（从0开始）
         */
        private Integer endCol;

        /**
         * 合并后的内容
         */
        private String content;
    }

    /**
     * 元数据信息
     */
    @Data
    public static class MetadataInfo {

        private String title;
        /**
         * 导出时间
         */
        private String exportTime;

        /**
         * 总行数
         */
        private Integer totalRows;

        /**
         * 总列数
         */
        private Integer totalColumns;

        /**
         * 表头行数
         */
        private Integer headerRows;

        /**
         * 是否包含数据行合并单元格
         */
        private Boolean hasMergedCells;

        /**
         * 是否包含表头合并单元格
         */
        private Boolean hasHeaderMerges;

        /**
         * 是否包含嵌套表格
         */
        private Boolean hasNestedTables;

        /**
         * 嵌套表格数量
         */
        private Integer nestedTableCount;

        /**
         * 嵌套表格总单元格数
         */
        private Integer totalNestedCells;

        /**
         * 是否有复杂结构（包含嵌套表格或合并单元格）
         */
        private Boolean hasComplexStructure;

        /**
         * 导出配置信息
         */
        private Map<String, Object> exportConfig;
    }

    /**
     * 单元格数据（支持公式和宽度配置）
     */
    @Data
    public static class CellData {
        /**
         * 单元格内容
         */
        private String content;

        /**
         * 是否包含数学公式
         */
        private Boolean hasMath;

        /**
         * MathML字符串（纯公式内容）
         */
        private String mathML;

        /**
         * 是否包含混合内容（公式+文本）
         */
        private Boolean hasMultipleContent;

        /**
         * 占位符到MathML的映射（混合内容）
         */
        private Map<String, String> mathMLMap;

        /**
         * 单元格宽度（像素）
         */
        private Integer width;

        /**
         * 单元格高度（像素）
         */
        private Integer height;

        /**
         * 嵌套表格配置
         */
        private NestedTableConfig nestedTable;
    }

    /**
     * 嵌套表格配置
     */
    @Data
    public static class NestedTableConfig {
        /**
         * 是否启用嵌套表格
         */
        private Boolean enabled;

        /**
         * 嵌套表格的配置信息
         */
        private NestedTableData config;

        /**
         * 嵌套表格的元数据
         */
        private NestedTableMetadata metadata;
    }

    /**
     * 嵌套表格数据
     */
    @Data
    public static class NestedTableData {
        /**
         * 表头配置
         */
        private HeaderConfig headerConfig;

        /**
         * 表头宽度配置
         */
        private HeaderWidthConfig headerWidthConfig;

        /**
         * 数据行
         */
        private List<List<CellData>> cellRows;

        /**
         * 合并单元格配置
         */
        private List<MergeConfig> merges;

        /**
         * 元数据
         */
        private Map<String, Object> metadata;
    }

    /**
     * 嵌套表格元数据
     */
    @Data
    public static class NestedTableMetadata {
        /**
         * 父单元格行索引
         */
        private Integer parentRowIndex;

        /**
         * 父单元格列索引
         */
        private Integer parentCellIndex;

        /**
         * 嵌套层级
         */
        private Integer nestedLevel;

        /**
         * 是否有主内容
         */
        private Boolean hasContent;
    }

    /**
     * 表头配置
     */
    @Data
    public static class HeaderConfig {
        /**
         * 表头数组
         */
        private List<List<String>> headers;

        /**
         * 表头合并配置
         */
        private List<MergeConfig> merges;
    }

    /**
     * 表头宽度配置
     */
    @Data
    public static class HeaderWidthConfig {
        /**
         * 列宽数组（像素）
         */
        private List<Integer> columnWidths;

        /**
         * 表头行高数组（像素）
         */
        private List<Integer> headerHeights;
    }
}
