import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                   // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true               // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect           // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    noCache: true                // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'               // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'             // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false            // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'   // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve),
      },
    ],
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true,
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true,
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true,
  },
  {
    path: '/word',
    component: (resolve) => require(['@/views/table.vue'], resolve),
    meta: {
      title: '表格管理',
      icon: 'el-icon-s-grid',
      description: '可调整大小的动态表格管理页面'
    },
    hidden: false,
  },
  {
    path: '/table-editor',
    component: (resolve) => require(['@/views/table-editor.vue'], resolve),
    meta: {
      title: '表格重构器',
      icon: 'el-icon-edit',
      description: '高级表格重构和编辑工具'
    },
    hidden: false,
  },
  {
    path: '/math-test',
    component: (resolve) => require(['@/views/test/math-test.vue'], resolve),
    meta: {
      title: '数学公式测试',
      icon: 'el-icon-s-grid',
      description: '数学公式测试页面'
    },
    hidden: false,
  },
  {
    path: '/json-table-demo',
    component: (resolve) => require(['@/views/test/json-table-demo.vue'], resolve),
    meta: {
      title: 'JSON表格演示',
      icon: 'el-icon-document-add',
      description: 'JSON数据插入和单元格合并演示'
    },
    hidden: false,
  },
  {
    path: '/check-record-config',
    component: (resolve) => require(['@/views/word/check-record-config.vue'], resolve),
    meta: {
      title: '检验记录配置',
      icon: 'el-icon-setting',
      description: '检验记录表配置和数据管理'
    },
    hidden: false,
  },
  {
    path: '/check-record-preview',
    component: (resolve) => require(['@/views/word/check-record-preview.vue'], resolve),
    meta: {
      title: '检验记录预览',
      icon: 'el-icon-view',
      description: '检验记录表预览和导出'
    },
    hidden: false,
  },
  {
    path: '/check-record-manage',
    component: (resolve) => require(['@/views/word/check-record-manage.vue'], resolve),
    meta: {
      title: '检验记录管理',
      icon: 'el-icon-edit-outline',
      description: '检验记录数据的新增、编辑和删除管理'
    },
    hidden: false,
  },
  {
    path: '/',
    component: Layout,
    redirect: '/json-table',
    meta: { title: '首页', icon: 'el-icon-house' },
    children: [
      {
        path: 'json-table',
        name: 'JsonTable',
        component: (resolve) => require(['@/views/JsonTableExample.vue'], resolve),
        meta: {
          title: 'JSON表格',
          icon: 'el-icon-data-analysis',
          description: 'JSON表格'
        }
      },
      {
        path: 'table-test',
        name: 'WidthTest',
        component: (resolve) => require(['@/views/JsonTableTest.vue'], resolve),
        meta: {
          title: 'JSON表格宽度测试',
          icon: 'el-icon-data-analysis',
          description: 'JSON表格宽度测试'
        }
      }
    ]
  },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

export default new Router({
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
})
